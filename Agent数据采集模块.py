# core/data_collector.py
import time
import json
import asyncio
from typing import Dict, List, Optional
from collections import deque
from utils.logger import get_logger
from utils.security import SecurityManager

logger = get_logger()

class DataCollector:
    def __init__(self, config, security_manager: SecurityManager):
        self.config = config
        self.security_manager = security_manager
        
        # 数据缓存队列
        self.alert_queue = deque(maxlen=10000)  # 最多缓存10000条告警
        self.stats_cache = {
            'total_connections': 0,
            'total_alerts': 0,
            'connections_by_port': {},
            'alerts_by_ip': {},
            'start_time': time.time()
        }
        
        # 批量上报配置
        self.batch_size = 50
        self.batch_timeout = 30  # 30秒超时批量发送
        self.last_batch_time = time.time()
    
    async def collect_connection_data(self, connection_info: Dict) -> Optional[Dict]:
        """收集连接数据并生成告警"""
        client_ip = connection_info['client_ip']
        port = connection_info['port']
        
        # 更新统计信息
        self._update_stats(client_ip, port)
        
        # 检查是否应该生成告警
        if not self.security_manager.is_allowed_connection(client_ip, port):
            logger.debug(f"Connection from {client_ip}:{port} filtered (whitelist/rate limit)")
            return None
        
        # 分析行为
        behavior = self.security_manager.analyze_behavior(client_ip)
        
        # 生成告警数据
        alert_data = self._create_alert_data(connection_info, behavior)
        
        # 添加到队列
        self.alert_queue.append(alert_data)
        self.stats_cache['total_alerts'] += 1
        
        logger.info(f"Alert generated: {client_ip}:{port} - {behavior['alert_level']}")
        
        return alert_data
    
    def _update_stats(self, client_ip: str, port: int):
        """更新统计信息"""
        self.stats_cache['total_connections'] += 1
        
        # 按端口统计
        if port not in self.stats_cache['connections_by_port']:
            self.stats_cache['connections_by_port'][port] = 0
        self.stats_cache['connections_by_port'][port] += 1
        
        # 按IP统计
        if client_ip not in self.stats_cache['alerts_by_ip']:
            self.stats_cache['alerts_by_ip'][client_ip] = 0
        self.stats_cache['alerts_by_ip'][client_ip] += 1
    
    def _create_alert_data(self, connection_info: Dict, behavior: Dict) -> Dict:
        """创建告警数据"""
        current_time = time.time()
        
        alert_data = {
            'timestamp': current_time,
            'agent_id': self.config.agent_id,
            'agent_name': self.config.agent_name,
            'source_ip': connection_info['client_ip'],
            'target_port': connection_info['port'],
            'protocol': connection_info['protocol'],
            'alert_level': behavior['alert_level'],
            
            # 连接详情
            'connection_details': {
                'banner_sent': connection_info.get('banner_sent', False),
                'data_received_length': len(connection_info.get('data_received', b'')),
                'response_sent_length': len(connection_info.get('response_sent', b'')),
                'connection_duration': connection_info.get('connection_duration', 0)
            },
            
            # 行为分析
            'behavior_analysis': {
                'is_port_scanning': behavior['is_port_scanning'],
                'is_frequent_connection': behavior['is_frequent_connection'],
                'connection_count_5min': behavior['connection_count_5min'],
                'port_scan_count_1min': behavior['port_scan_count_1min']
            },
            
            # 地理位置信息（如果可用）
            'geo_info': self.security_manager.get_geo_info(connection_info['client_ip']),
            
            # 原始数据（用于调试）
            'raw_data': {
                'data_received': connection_info.get('data_received', b'').hex() if connection_info.get('data_received') else '',
                'response_sent': connection_info.get('response_sent', b'').hex() if connection_info.get('response_sent') else ''
            }
        }
        
        return alert_data
    
    def get_pending_alerts(self, max_count: int = None) -> List[Dict]:
        """获取待发送的告警数据"""
        if max_count is None:
            max_count = self.batch_size
        
        alerts = []
        for _ in range(min(max_count, len(self.alert_queue))):
            if self.alert_queue:
                alerts.append(self.alert_queue.popleft())
        
        return alerts
    
    def should_send_batch(self) -> bool:
        """判断是否应该批量发送数据"""
        current_time = time.time()
        
        # 队列达到批量大小
        if len(self.alert_queue) >= self.batch_size:
            return True
        
        # 超时且有数据
        if (current_time - self.last_batch_time >= self.batch_timeout and 
            len(self.alert_queue) > 0):
            return True
        
        return False
    
    def mark_batch_sent(self):
        """标记批量数据已发送"""
        self.last_batch_time = time.time()
    
    def get_stats(self) -> Dict:
        """获取统计信息"""
        current_time = time.time()
        uptime = current_time - self.stats_cache['start_time']
        
        stats = {
            'agent_info': {
                'agent_id': self.config.agent_id,
                'agent_name': self.config.agent_name,
                'uptime_seconds': uptime,
                'monitored_ports': self.config.monitoring.ports
            },
            'connection_stats': {
                'total_connections': self.stats_cache['total_connections'],
                'total_alerts': self.stats_cache['total_alerts'],
                'connections_per_hour': int(self.stats_cache['total_connections'] / (uptime / 3600)) if uptime > 0 else 0,
                'alerts_per_hour': int(self.stats_cache['total_alerts'] / (uptime / 3600)) if uptime > 0 else 0
            },
            'port_distribution': dict(sorted(
                self.stats_cache['connections_by_port'].items(),
                key=lambda x: x[1],
                reverse=True
            )[:10]),  # 前10个最活跃的端口
            'top_attackers': dict(sorted(
                self.stats_cache['alerts_by_ip'].items(),
                key=lambda x: x[1],
                reverse=True
            )[:10]),  # 前10个最活跃的攻击者IP
            'queue_status': {
                'pending_alerts': len(self.alert_queue),
                'queue_capacity': self.alert_queue.maxlen,
                'last_batch_time': self.last_batch_time
            }
        }
        
        return stats
    
    def get_recent_alerts(self, count: int = 100) -> List[Dict]:
        """获取最近的告警（不移除）"""
        return list(self.alert_queue)[-count:] if self.alert_queue else []
    
    def clear_stats(self):
        """清空统计信息"""
        self.stats_cache = {
            'total_connections': 0,
            'total_alerts': 0,
            'connections_by_port': {},
            'alerts_by_ip': {},
            'start_time': time.time()
        }
        logger.info("Statistics cleared")
    
    def export_data(self, file_path: str = None) -> str:
        """导出数据到文件"""
        if file_path is None:
            timestamp = int(time.time())
            file_path = f"honeypot_data_{timestamp}.json"
        
        export_data = {
            'export_time': time.time(),
            'stats': self.get_stats(),
            'recent_alerts': self.get_recent_alerts(1000)  # 导出最近1000条告警
        }
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"Data exported to {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"Failed to export data: {e}")
            raise
    
    def cleanup_old_data(self, max_age_hours: int = 24):
        """清理过期数据"""
        current_time = time.time()
        cutoff_time = current_time - (max_age_hours * 3600)
        
        # 清理过期的告警数据
        original_count = len(self.alert_queue)
        filtered_alerts = deque(maxlen=self.alert_queue.maxlen)
        
        for alert in self.alert_queue:
            if alert.get('timestamp', 0) >= cutoff_time:
                filtered_alerts.append(alert)
        
        self.alert_queue = filtered_alerts
        cleaned_count = original_count - len(self.alert_queue)
        
        if cleaned_count > 0:
            logger.info(f"Cleaned up {cleaned_count} old alert records")
        
        # 清理安全管理器中的过期记录
        self.security_manager.periodic_cleanup()