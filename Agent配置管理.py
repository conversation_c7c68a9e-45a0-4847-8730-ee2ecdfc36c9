# config/settings.py
import yaml
import os
from pathlib import Path
from typing import Dict, List, Optional
from dataclasses import dataclass, field

@dataclass
class ManagementConfig:
    server_url: str
    api_key: str
    heartbeat_interval: int = 60
    reconnect_interval: int = 30
    max_reconnect_attempts: int = 10

@dataclass
class MonitoringConfig:
    ports: List[int] = field(default_factory=lambda: [22, 21, 23, 80, 443, 3389, 1433, 3306])
    ssh_management_port: int = 2222
    max_connections: int = 1000
    connection_timeout: int = 30
    bind_address: str = "0.0.0.0"

@dataclass
class ResponseConfig:
    mode: str = "banner"  # none, banner, limited_interactive
    banners: Dict[int, str] = field(default_factory=lambda: {
        22: "SSH-2.0-OpenSSH_8.0",
        21: "220 FTP server ready",
        23: "login: ",
        80: "HTTP/1.1 404 Not Found\r\nServer: nginx\r\n\r\n",
        443: ""
    })
    delay_before_close: float = 0.1
    max_interactions: int = 1

@dataclass
class SecurityConfig:
    allowed_management_ips: List[str] = field(default_factory=list)
    log_level: str = "INFO"
    max_log_size: str = "100MB"
    log_rotation: int = 7

@dataclass
class FilteringConfig:
    whitelist_ips: List[str] = field(default_factory=list)
    ignore_internal: bool = True
    min_alert_interval: int = 60
    known_scanners: List[str] = field(default_factory=lambda: [
        "shodan.io", "censys.io", "shadowserver.org"
    ])

@dataclass
class AgentConfig:
    management: ManagementConfig
    monitoring: MonitoringConfig
    responses: ResponseConfig = field(default_factory=ResponseConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)
    filtering: FilteringConfig = field(default_factory=FilteringConfig)
    
    agent_id: Optional[str] = None
    agent_name: Optional[str] = None

class ConfigManager:
    def __init__(self, config_path: str = "config/agent.yaml"):
        self.config_path = Path(config_path)
        self.config: Optional[AgentConfig] = None
        self.load_config()
    
    def load_config(self) -> AgentConfig:
        """加载配置文件"""
        try:
            if not self.config_path.exists():
                self._create_default_config()
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            self.config = self._parse_config(config_data)
            return self.config
            
        except Exception as e:
            raise Exception(f"Failed to load config: {e}")
    
    def _parse_config(self, data: dict) -> AgentConfig:
        """解析配置数据"""
        management = ManagementConfig(**data.get('management', {}))
        monitoring = MonitoringConfig(**data.get('monitoring', {}))
        responses = ResponseConfig(**data.get('responses', {}))
        security = SecurityConfig(**data.get('security', {}))
        filtering = FilteringConfig(**data.get('filtering', {}))
        
        return AgentConfig(
            management=management,
            monitoring=monitoring,
            responses=responses,
            security=security,
            filtering=filtering,
            agent_id=data.get('agent_id'),
            agent_name=data.get('agent_name', f"agent-{os.uname().nodename}")
        )
    
    def _create_default_config(self):
        """创建默认配置文件"""
        default_config = {
            'agent_id': None,
            'agent_name': f"agent-{os.uname().nodename}",
            'management': {
                'server_url': 'wss://mgmt.example.com/ws',
                'api_key': 'your-api-key-here',
                'heartbeat_interval': 60,
                'reconnect_interval': 30
            },
            'monitoring': {
                'ports': [22, 21, 23, 80, 443, 3389, 1433, 3306],
                'ssh_management_port': 2222,
                'max_connections': 1000,
                'connection_timeout': 30
            },
            'responses': {
                'mode': 'banner',
                'banners': {
                    22: 'SSH-2.0-OpenSSH_8.0',
                    21: '220 FTP server ready',
                    23: 'login: ',
                    80: 'HTTP/1.1 404 Not Found\r\nServer: nginx\r\n\r\n',
                    443: ''
                }
            },
            'security': {
                'allowed_management_ips': [],
                'log_level': 'INFO',
                'max_log_size': '100MB',
                'log_rotation': 7
            },
            'filtering': {
                'whitelist_ips': ['127.0.0.1', '***********/16', '10.0.0.0/8'],
                'ignore_internal': True,
                'min_alert_interval': 60
            }
        }
        
        # 确保目录存在
        self.config_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(self.config_path, 'w', encoding='utf-8') as f:
            yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)
    
    def save_config(self):
        """保存配置到文件"""
        if not self.config:
            return
        
        config_dict = {
            'agent_id': self.config.agent_id,
            'agent_name': self.config.agent_name,
            'management': {
                'server_url': self.config.management.server_url,
                'api_key': self.config.management.api_key,
                'heartbeat_interval': self.config.management.heartbeat_interval,
                'reconnect_interval': self.config.management.reconnect_interval
            },
            'monitoring': {
                'ports': self.config.monitoring.ports,
                'ssh_management_port': self.config.monitoring.ssh_management_port,
                'max_connections': self.config.monitoring.max_connections,
                'connection_timeout': self.config.monitoring.connection_timeout
            },
            'responses': {
                'mode': self.config.responses.mode,
                'banners': self.config.responses.banners
            },
            'security': {
                'allowed_management_ips': self.config.security.allowed_management_ips,
                'log_level': self.config.security.log_level,
                'max_log_size': self.config.security.max_log_size,
                'log_rotation': self.config.security.log_rotation
            },
            'filtering': {
                'whitelist_ips': self.config.filtering.whitelist_ips,
                'ignore_internal': self.config.filtering.ignore_internal,
                'min_alert_interval': self.config.filtering.min_alert_interval
            }
        }
        
        with open(self.config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
    
    def update_config(self, updates: dict):
        """更新配置"""
        if not self.config:
            return
        
        # 这里可以实现配置的动态更新逻辑
        # 暂时重新加载配置文件
        self.load_config()
    
    def get_config(self) -> AgentConfig:
        """获取当前配置"""
        if not self.config:
            self.load_config()
        return self.config