# core/port_monitor.py
import asyncio
import socket
from typing import Dict, List, Optional, Set
from utils.logger import get_logger
from core.response_handler import ResponseHandler
from core.data_collector import DataCollector

logger = get_logger()

class PortMonitor:
    def __init__(self, config, response_handler: <PERSON><PERSON><PERSON><PERSON>, data_collector: DataCollector):
        self.config = config
        self.response_handler = response_handler
        self.data_collector = data_collector
        
        # 监听配置
        self.monitored_ports = config.monitoring.ports
        self.bind_address = config.monitoring.bind_address
        self.max_connections = config.monitoring.max_connections
        self.connection_timeout = config.monitoring.connection_timeout
        
        # 服务器实例
        self.servers: Dict[int, asyncio.Server] = {}
        self.active_connections: Set[asyncio.Task] = set()
        
        # 统计信息
        self.connection_count = 0
        self.total_connections = 0
        
        # 控制标志
        self.running = False
    
    async def start(self):
        """启动端口监控"""
        if self.running:
            logger.warning("Port monitor is already running")
            return
        
        logger.info("Starting port monitor...")
        self.running = True
        
        # 检查端口冲突
        await self._check_port_conflicts()
        
        # 启动所有端口监听
        for port in self.monitored_ports:
            try:
                await self._start_port_listener(port)
            except Exception as e:
                logger.error(f"Failed to start listener on port {port}: {e}")
        
        # 启动连接清理任务
        asyncio.create_task(self._cleanup_connections())
        
        logger.info(f"Port monitor started, monitoring {len(self.servers)} ports")
    
    async def stop(self):
        """停止端口监控"""
        if not self.running:
            return
        
        logger.info("Stopping port monitor...")
        self.running = False
        
        # 停止所有服务器
        for port, server in self.servers.items():
            logger.info(f"Stopping listener on port {port}")
            server.close()
            await server.wait_closed()
        
        # 等待所有活动连接完成
        if self.active_connections:
            logger.info(f"Waiting for {len(self.active_connections)} active connections to finish...")
            await asyncio.gather(*self.active_connections, return_exceptions=True)
        
        self.servers.clear()
        self.active_connections.clear()
        
        logger.info("Port monitor stopped")
    
    async def _check_port_conflicts(self):
        """检查端口冲突"""
        conflicts = []
        
        for port in self.monitored_ports:
            if await self._is_port_in_use(port):
                conflicts.append(port)
        
        if conflicts:
            logger.warning(f"Ports already in use: {conflicts}")
            # 从监控列表中移除冲突端口
            self.monitored_ports = [p for p in self.monitored_ports if p not in conflicts]
            logger.info(f"Monitoring ports adjusted to: {self.monitored_ports}")
    
    async def _is_port_in_use(self, port: int) -> bool:
        """检查端口是否被占用"""
        try:
            # 尝试绑定端口
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            sock.bind((self.bind_address, port))
            sock.close()
            return False
        except OSError:
            return True
    
    async def _start_port_listener(self, port: int):
        """启动单个端口监听"""
        try:
            server = await asyncio.start_server(
                lambda r, w: self._handle_connection(r, w, port),
                host=self.bind_address,
                port=port,
                reuse_address=True,
                reuse_port=True
            )
            
            self.servers[port] = server
            logger.info(f"Started listener on {self.bind_address}:{port}")
            
        except Exception as e:
            logger.error(f"Failed to bind port {port}: {e}")
            raise
    
    async def _handle_connection(self, reader: asyncio.StreamReader, 
                               writer: asyncio.StreamWriter, port: int):
        """处理新连接"""
        client_address = writer.get_extra_info('peername')
        client_ip = client_address[0] if client_address else 'unknown'
        
        # 检查连接数限制
        if len(self.active_connections) >= self.max_connections:
            logger.warning(f"Connection limit reached, rejecting {client_ip}:{port}")
            writer.close()
            await writer.wait_closed()
            return
        
        # 创建连接处理任务
        task = asyncio.create_task(
            self._process_connection(reader, writer, port, client_ip)
        )
        self.active_connections.add(task)
        
        # 设置任务完成回调
        task.add_done_callback(lambda t: self.active_connections.discard(t))
    
    async def _process_connection(self, reader: asyncio.StreamReader,
                                writer: asyncio.StreamWriter, 
                                port: int, client_ip: str):
        """处理连接的完整流程"""
        connection_start_time = asyncio.get_event_loop().time()
        
        try:
            # 更新统计
            self.connection_count += 1
            self.total_connections += 1
            
            logger.debug(f"New connection from {client_ip}:{port} (active: {self.connection_count})")
            
            # 设置连接超时
            try:
                connection_info = await asyncio.wait_for(
                    self.response_handler.handle_connection(reader, writer, port, client_ip),
                    timeout=self.connection_timeout
                )
            except asyncio.TimeoutError:
                logger.debug(f"Connection timeout for {client_ip}:{port}")
                connection_info = {
                    'client_ip': client_ip,
                    'port': port,
                    'protocol': self.response_handler._detect_protocol(port),
                    'timeout': True
                }
            
            # 收集数据并生成告警
            alert_data = await self.data_collector.collect_connection_data(connection_info)
            
            # 如果是高优先级告警，立即发送
            if alert_data and alert_data.get('alert_level') == 'CRITICAL':
                # 这里可以添加立即发送逻辑
                pass
            
        except Exception as e:
            logger.error(f"Error processing connection from {client_ip}:{port}: {e}")
        
        finally:
            # 更新统计
            self.connection_count -= 1
            connection_duration = asyncio.get_event_loop().time() - connection_start_time
            
            logger.debug(f"Connection from {client_ip}:{port} finished "
                        f"(duration: {connection_duration:.2f}s, active: {self.connection_count})")
    
    async def _cleanup_connections(self):
        """定期清理连接任务"""
        while self.running:
            try:
                # 清理已完成的任务
                completed_tasks = [task for task in self.active_connections if task.done()]
                for task in completed_tasks:
                    self.active_connections.discard(task)
                
                if completed_tasks:
                    logger.debug(f"Cleaned up {len(completed_tasks)} completed connection tasks")
                
                await asyncio.sleep(30)  # 每30秒清理一次
                
            except Exception as e:
                logger.error(f"Error in connection cleanup: {e}")
                await asyncio.sleep(60)  # 出错时等待更长时间
    
    def get_status(self) -> Dict:
        """获取监控状态"""
        return {
            'running': self.running,
            'monitored_ports': self.monitored_ports,
            'active_servers': list(self.servers.keys()),
            'active_connections': len(self.active_connections),
            'total_connections': self.total_connections,
            'max_connections': self.max_connections
        }
    
    def get_port_stats(self) -> Dict[int, Dict]:
        """获取各端口统计信息"""
        stats = {}
        for port in self.monitored_ports:
            stats[port] = {
                'listening': port in self.servers,
                'protocol': self.response_handler._detect_protocol(port),
                'has_banner': bool(self.response_handler.get_banner(port))
            }
        return stats
    
    async def add_port(self, port: int) -> bool:
        """动态添加监控端口"""
        if port in self.monitored_ports:
            logger.warning(f"Port {port} is already being monitored")
            return False
        
        if await self._is_port_in_use(port):
            logger.error(f"Cannot add port {port}: already in use")
            return False
        
        try:
            await self._start_port_listener(port)
            self.monitored_ports.append(port)
            logger.info(f"Added monitoring for port {port}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add port {port}: {e}")
            return False
    
    async def remove_port(self, port: int) -> bool:
        """动态移除监控端口"""
        if port not in self.monitored_ports:
            logger.warning(f"Port {port} is not being monitored")
            return False
        
        if port in self.servers:
            try:
                server = self.servers[port]
                server.close()
                await server.wait_closed()
                del self.servers[port]
                
                self.monitored_ports.remove(port)
                logger.info(f"Removed monitoring for port {port}")
                return True
                
            except Exception as e:
                logger.error(f"Failed to remove port {port}: {e}")
                return False
        
        return False
    
    async def reload_config(self, new_ports: List[int]):
        """重新加载端口配置"""
        logger.info(f"Reloading port configuration: {new_ports}")
        
        # 停止不再需要的端口
        ports_to_remove = set(self.monitored_ports) - set(new_ports)
        for port in ports_to_remove:
            await self.remove_port(port)
        
        # 添加新端口
        ports_to_add = set(new_ports) - set(self.monitored_ports)
        for port in ports_to_add:
            await self.add_port(port)
        
        logger.info(f"Port configuration reloaded, now monitoring: {self.monitored_ports}")