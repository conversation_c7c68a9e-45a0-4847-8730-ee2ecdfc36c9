# core/communication.py
import asyncio
import json
import ssl
import uuid
import websockets
from typing import Dict, Optional, Callable
from urllib.parse import urlparse
from utils.logger import get_logger

logger = get_logger()

class CommunicationManager:
    def __init__(self, config, data_collector):
        self.config = config
        self.data_collector = data_collector
        
        # WebSocket连接
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        self.connected = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = config.management.max_reconnect_attempts
        
        # 心跳相关
        self.heartbeat_task: Optional[asyncio.Task] = None
        self.heartbeat_interval = config.management.heartbeat_interval
        
        # 批量发送相关
        self.batch_send_task: Optional[asyncio.Task] = None
        
        # 消息处理回调
        self.message_handlers: Dict[str, Callable] = {}
        self._setup_message_handlers()
        
        # 连接状态回调
        self.on_connected: Optional[Callable] = None
        self.on_disconnected: Optional[Callable] = None
    
    def _setup_message_handlers(self):
        """设置消息处理器"""
        self.message_handlers = {
            'config_update': self._handle_config_update,
            'stats_request': self._handle_stats_request,
            'ping': self._handle_ping,
            'command': self._handle_command
        }
    
    async def start(self):
        """启动通信管理器"""
        logger.info("Starting communication manager...")
        
        # 启动WebSocket连接
        await self._connect_websocket()
        
        # 启动批量发送任务
        self.batch_send_task = asyncio.create_task(self._batch_send_loop())
        
        logger.info("Communication manager started")
    
    async def stop(self):
        """停止通信管理器"""
        logger.info("Stopping communication manager...")
        
        # 停止心跳任务
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
            try:
                await self.heartbeat_task
            except asyncio.CancelledError:
                pass
        
        # 停止批量发送任务
        if self.batch_send_task:
            self.batch_send_task.cancel()
            try:
                await self.batch_send_task
            except asyncio.CancelledError:
                pass
        
        # 关闭WebSocket连接
        if self.websocket:
            await self.websocket.close()
        
        logger.info("Communication manager stopped")
    
    async def _connect_websocket(self):
        """连接到管理端WebSocket"""
        while self.reconnect_attempts < self.max_reconnect_attempts:
            try:
                # 解析WebSocket URL
                url = self.config.management.server_url
                if not url.startswith(('ws://', 'wss://')):
                    logger.error(f"Invalid WebSocket URL: {url}")
                    return
                
                # 构建连接URL
                agent_id = self.config.agent_id or str(uuid.uuid4())
                connect_url = f"{url}/agent/{agent_id}"
                
                # 设置SSL上下文（如果是wss）
                ssl_context = None
                if url.startswith('wss://'):
                    ssl_context = ssl.create_default_context()
                    # 在生产环境中，应该验证证书
                    ssl_context.check_hostname = False
                    ssl_context.verify_mode = ssl.CERT_NONE
                
                # 设置连接头
                headers = {
                    'Authorization': f'Bearer {self.config.management.api_key}',
                    'Agent-Name': self.config.agent_name,
                    'Agent-Version': '1.0.0'
                }
                
                logger.info(f"Connecting to {connect_url}...")
                
                # 建立WebSocket连接
                self.websocket = await websockets.connect(
                    connect_url,
                    ssl=ssl_context,
                    extra_headers=headers,
                    ping_interval=30,
                    ping_timeout=10
                )
                
                self.connected = True
                self.reconnect_attempts = 0
                
                logger.info("WebSocket connected successfully")
                
                # 发送注册消息
                await self._send_registration()
                
                # 启动心跳任务
                self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
                
                # 启动消息接收循环
                await self._message_receive_loop()
                
            except Exception as e:
                self.connected = False
                self.reconnect_attempts += 1
                logger.error(f"WebSocket connection failed (attempt {self.reconnect_attempts}): {e}")
                
                if self.reconnect_attempts < self.max_reconnect_attempts:
                    wait_time = min(30, 2 ** self.reconnect_attempts)  # 指数退避
                    logger.info(f"Retrying in {wait_time} seconds...")
                    await asyncio.sleep(wait_time)
                else:
                    logger.error("Max reconnection attempts reached")
                    break
    
    async def _send_registration(self):
        """发送Agent注册信息"""
        registration_data = {
            'type': 'registration',
            'data': {
                'agent_id': self.config.agent_id,
                'agent_name': self.config.agent_name,
                'monitored_ports': self.config.monitoring.ports,
                'response_mode': self.config.responses.mode,
                'version': '1.0.0',
                'capabilities': ['port_monitoring', 'banner_response', 'behavior_analysis']
            }
        }
        
        await self._send_message(registration_data)
        logger.info("Registration message sent")
    
    async def _heartbeat_loop(self):
        """心跳循环"""
        while self.connected:
            try:
                # 发送心跳消息
                heartbeat_data = {
                    'type': 'heartbeat',
                    'data': {
                        'timestamp': asyncio.get_event_loop().time(),
                        'status': 'online',
                        'stats': self.data_collector.get_stats()
                    }
                }
                
                await self._send_message(heartbeat_data)
                logger.debug("Heartbeat sent")
                
                await asyncio.sleep(self.heartbeat_interval)
                
            except Exception as e:
                logger.error(f"Heartbeat error: {e}")
                self.connected = False
                break
    
    async def _batch_send_loop(self):
        """批量发送循环"""
        while True:
            try:
                # 检查是否需要发送批量数据
                if self.connected and self.data_collector.should_send_batch():
                    alerts = self.data_collector.get_pending_alerts()
                    if alerts:
                        await self._send_alerts_batch(alerts)
                        self.data_collector.mark_batch_sent()
                
                await asyncio.sleep(5)  # 每5秒检查一次
                
            except Exception as e:
                logger.error(f"Batch send error: {e}")
                await asyncio.sleep(10)  # 出错时等待更长时间
    
    async def _send_alerts_batch(self, alerts: list):
        """发送告警批量数据"""
        batch_data = {
            'type': 'alerts_batch',
            'data': {
                'alerts': alerts,
                'batch_size': len(alerts),
                'timestamp': asyncio.get_event_loop().time()
            }
        }
        
        await self._send_message(batch_data)
        logger.info(f"Sent batch of {len(alerts)} alerts")
    
    async def _message_receive_loop(self):
        """消息接收循环"""
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    await self._handle_message(data)
                except json.JSONDecodeError as e:
                    logger.warning(f"Invalid JSON message received: {e}")
                except Exception as e:
                    logger.error(f"Error handling message: {e}")
        except websockets.exceptions.ConnectionClosed:
            logger.warning("WebSocket connection closed")
            self.connected = False
        except Exception as e:
            logger.error(f"Message receive loop error: {e}")
            self.connected = False
    
    async def _handle_message(self, data: dict):
        """处理接收到的消息"""
        message_type = data.get('type')
        if not message_type:
            logger.warning("Message without type received")
            return
        
        handler = self.message_handlers.get(message_type)
        if handler:
            await handler(data.get('data', {}))
        else:
            logger.warning(f"Unknown message type: {message_type}")
    
    async def _handle_config_update(self, data: dict):
        """处理配置更新"""
        logger.info("Received configuration update")
        try:
            # 这里可以实现配置的动态更新
            # 暂时只记录日志
            logger.info(f"Configuration update data: {data}")
            
            # 发送确认消息
            response = {
                'type': 'config_update_ack',
                'data': {
                    'status': 'success',
                    'message': 'Configuration updated successfully'
                }
            }
            await self._send_message(response)
            
        except Exception as e:
            logger.error(f"Error updating configuration: {e}")
            # 发送错误响应
            response = {
                'type': 'config_update_ack',
                'data': {
                    'status': 'error',
                    'message': str(e)
                }
            }
            await self._send_message(response)
    
    async def _handle_stats_request(self, data: dict):
        """处理统计信息请求"""
        logger.debug("Received stats request")
        try:
            stats = self.data_collector.get_stats()
            response = {
                'type': 'stats_response',
                'data': stats
            }
            await self._send_message(response)
            
        except Exception as e:
            logger.error(f"Error getting stats: {e}")
    
    async def _handle_ping(self, data: dict):
        """处理ping消息"""
        logger.debug("Received ping")
        response = {
            'type': 'pong',
            'data': {
                'timestamp': asyncio.get_event_loop().time()
            }
        }
        await self._send_message(response)
    
    async def _handle_command(self, data: dict):
        """处理命令消息"""
        command = data.get('command')
        logger.info(f"Received command: {command}")
        
        try:
            result = await self._execute_command(command, data.get('params', {}))
            response = {
                'type': 'command_response',
                'data': {
                    'command': command,
                    'status': 'success',
                    'result': result
                }
            }
        except Exception as e:
            logger.error(f"Error executing command {command}: {e}")
            response = {
                'type': 'command_response',
                'data': {
                    'command': command,
                    'status': 'error',
                    'error': str(e)
                }
            }
        
        await self._send_message(response)
    
    async def _execute_command(self, command: str, params: dict):
        """执行命令"""
        if command == 'get_stats':
            return self.data_collector.get_stats()
        elif command == 'clear_stats':
            self.data_collector.clear_stats()
            return {'message': 'Statistics cleared'}
        elif command == 'export_data':
            file_path = self.data_collector.export_data(params.get('file_path'))
            return {'file_path': file_path}
        elif command == 'get_recent_alerts':
            count = params.get('count', 100)
            return self.data_collector.get_recent_alerts(count)
        else:
            raise ValueError(f"Unknown command: {command}")
    
    async def _send_message(self, data: dict):
        """发送消息"""
        if not self.connected or not self.websocket:
            logger.warning("Cannot send message: not connected")
            return
        
        try:
            message = json.dumps(data, ensure_ascii=False, default=str)
            await self.websocket.send(message)
            logger.debug(f"Message sent: {data.get('type', 'unknown')}")
            
        except Exception as e:
            logger.error(f"Error sending message: {e}")
            self.connected = False
    
    def is_connected(self) -> bool:
        """检查连接状态"""
        return self.connected and self.websocket and not self.websocket.closed
    
    async def send_immediate_alert(self, alert_data: dict):
        """立即发送单个告警（用于高优先级告警）"""
        if not self.is_connected():
            logger.warning("Cannot send immediate alert: not connected")
            return
        
        immediate_alert = {
            'type': 'immediate_alert',
            'data': alert_data
        }
        
        await self._send_message(immediate_alert)
        logger.info(f"Immediate alert sent for {alert_data.get('source_ip')}:{alert_data.get('target_port')}")