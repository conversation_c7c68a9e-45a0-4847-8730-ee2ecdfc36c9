# main.py
import asyncio
import signal
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import ConfigManager
from utils.logger import init_logger
from utils.security import SecurityManager
from core.response_handler import ResponseHandler
from core.data_collector import DataCollector
from core.communication import CommunicationManager
from core.port_monitor import PortMonitor

class HoneypotAgent:
    def __init__(self, config_path: str = "config/agent.yaml"):
        # 加载配置
        self.config_manager = ConfigManager(config_path)
        self.config = self.config_manager.get_config()
        
        # 初始化日志
        self.logger = init_logger(self.config)
        
        # 初始化各个模块
        self.security_manager = SecurityManager(self.config)
        self.response_handler = ResponseHandler(self.config)
        self.data_collector = DataCollector(self.config, self.security_manager)
        self.communication_manager = CommunicationManager(self.config, self.data_collector)
        self.port_monitor = PortMonitor(self.config, self.response_handler, self.data_collector)
        
        # 运行状态
        self.running = False
        self.shutdown_event = asyncio.Event()
    
    async def start(self):
        """启动Agent"""
        if self.running:
            self.logger.warning("Agent is already running")
            return
        
        self.logger.info("Starting Honeypot Agent...")
        self.logger.info(f"Agent ID: {self.config.agent_id}")
        self.logger.info(f"Agent Name: {self.config.agent_name}")
        self.logger.info(f"Monitored Ports: {self.config.monitoring.ports}")
        self.logger.info(f"Response Mode: {self.config.responses.mode}")
        
        try:
            self.running = True
            
            # 启动端口监控
            await self.port_monitor.start()
            
            # 启动通信管理器
            await self.communication_manager.start()
            
            self.logger.info("Honeypot Agent started successfully")
            
            # 等待关闭信号
            await self.shutdown_event.wait()
            
        except Exception as e:
            self.logger.error(f"Error starting agent: {e}")
            raise
    
    async def stop(self):
        """停止Agent"""
        if not self.running:
            return
        
        self.logger.info("Stopping Honeypot Agent...")
        self.running = False
        
        try:
            # 停止各个组件
            await self.port_monitor.stop()
            await self.communication_manager.stop()
            
            self.logger.info("Honeypot Agent stopped successfully")
            
        except Exception as e:
            self.logger.error(f"Error stopping agent: {e}")
        
        finally:
            self.shutdown_event.set()
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.logger.info(f"Received signal {signum}, shutting down...")
            asyncio.create_task(self.stop())
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Windows系统不支持SIGUSR1
        if hasattr(signal, 'SIGUSR1'):
            def reload_handler(signum, frame):
                self.logger.info("Received reload signal, reloading configuration...")
                asyncio.create_task(self.reload_config())
            
            signal.signal(signal.SIGUSR1, reload_handler)
    
    async def reload_config(self):
        """重新加载配置"""
        try:
            self.logger.info("Reloading configuration...")
            
            # 重新加载配置文件
            old_ports = self.config.monitoring.ports.copy()
            self.config = self.config_manager.load_config()
            new_ports = self.config.monitoring.ports
            
            # 如果端口配置发生变化，重新加载端口监控
            if old_ports != new_ports:
                await self.port_monitor.reload_config(new_ports)
            
            # 更新其他组件的配置
            self.security_manager = SecurityManager(self.config)
            self.response_handler = ResponseHandler(self.config)
            
            self.logger.info("Configuration reloaded successfully")
            
        except Exception as e:
            self.logger.error(f"Error reloading configuration: {e}")
    
    def get_status(self) -> dict:
        """获取Agent状态"""
        return {
            'running': self.running,
            'config': {
                'agent_id': self.config.agent_id,
                'agent_name': self.config.agent_name,
                'monitored_ports': self.config.monitoring.ports,
                'response_mode': self.config.responses.mode
            },
            'port_monitor': self.port_monitor.get_status(),
            'communication': {
                'connected': self.communication_manager.is_connected(),
                'server_url': self.config.management.server_url
            },
            'data_collector': self.data_collector.get_stats()
        }
    
    async def run_interactive_mode(self):
        """运行交互模式（用于调试）"""
        self.logger.info("Starting interactive mode...")
        
        # 启动Agent
        start_task = asyncio.create_task(self.start())
        
        # 交互命令循环
        while self.running:
            try:
                command = await asyncio.get_event_loop().run_in_executor(
                    None, input, "honeypot> "
                )
                
                await self.handle_interactive_command(command.strip())
                
            except (EOFError, KeyboardInterrupt):
                break
            except Exception as e:
                self.logger.error(f"Error in interactive mode: {e}")
        
        await self.stop()
        await start_task
    
    async def handle_interactive_command(self, command: str):
        """处理交互命令"""
        if not command:
            return
        
        parts = command.split()
        cmd = parts[0].lower()
        
        if cmd == 'status':
            status = self.get_status()
            print(f"Agent Status: {'Running' if status['running'] else 'Stopped'}")
            print(f"Monitored Ports: {status['config']['monitored_ports']}")
            print(f"Active Connections: {status['port_monitor']['active_connections']}")
            print(f"Total Connections: {status['port_monitor']['total_connections']}")
            print(f"Communication: {'Connected' if status['communication']['connected'] else 'Disconnected'}")
            
        elif cmd == 'stats':
            stats = self.data_collector.get_stats()
            print(f"Total Connections: {stats['connection_stats']['total_connections']}")
            print(f"Total Alerts: {stats['connection_stats']['total_alerts']}")
            print(f"Connections/Hour: {stats['connection_stats']['connections_per_hour']}")
            print(f"Alerts/Hour: {stats['connection_stats']['alerts_per_hour']}")
            
        elif cmd == 'ports':
            port_stats = self.port_monitor.get_port_stats()
            for port, info in port_stats.items():
                status = "Listening" if info['listening'] else "Not Listening"
                print(f"Port {port} ({info['protocol']}): {status}")
            
        elif cmd == 'alerts':
            count = int(parts[1]) if len(parts) > 1 else 10
            alerts = self.data_collector.get_recent_alerts(count)
            print(f"Recent {len(alerts)} alerts:")
            for alert in alerts[-10:]:  # 显示最近10条
                print(f"  {alert['timestamp']:.0f}: {alert['source_ip']}:{alert['target_port']} - {alert['alert_level']}")
            
        elif cmd == 'reload':
            await self.reload_config()
            print("Configuration reloaded")
            
        elif cmd == 'export':
            file_path = parts[1] if len(parts) > 1 else None
            exported_file = self.data_collector.export_data(file_path)
            print(f"Data exported to: {exported_file}")
            
        elif cmd == 'clear':
            self.data_collector.clear_stats()
            print("Statistics cleared")
            
        elif cmd in ['quit', 'exit']:
            await self.stop()
            
        elif cmd == 'help':
            print("Available commands:")
            print("  status  - Show agent status")
            print("  stats   - Show statistics")
            print("  ports   - Show port status")
            print("  alerts [count] - Show recent alerts")
            print("  reload  - Reload configuration")
            print("  export [file] - Export data")
            print("  clear   - Clear statistics")
            print("  quit/exit - Stop agent")
            
        else:
            print(f"Unknown command: {command}")
            print("Type 'help' for available commands")


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Honeypot Agent')
    parser.add_argument('-c', '--config', default='config/agent.yaml',
                       help='Configuration file path')
    parser.add_argument('-i', '--interactive', action='store_true',
                       help='Run in interactive mode')
    parser.add_argument('-d', '--daemon', action='store_true',
                       help='Run as daemon')
    parser.add_argument('--generate-config', action='store_true',
                       help='Generate default configuration file')
    
    args = parser.parse_args()
    
    # 生成默认配置
    if args.generate_config:
        config_manager = ConfigManager(args.config)
        print(f"Default configuration generated at: {args.config}")
        return
    
    # 创建Agent实例
    agent = HoneypotAgent(args.config)
    
    # 设置信号处理器
    agent.setup_signal_handlers()
    
    try:
        if args.interactive:
            # 交互模式
            await agent.run_interactive_mode()
        else:
            # 正常运行模式
            await agent.start()
            
    except KeyboardInterrupt:
        agent.logger.info("Received keyboard interrupt")
    except Exception as e:
        agent.logger.error(f"Unexpected error: {e}")
        raise
    finally:
        await agent.stop()


if __name__ == "__main__":
    # 确保在Python 3.7+上运行
    if sys.version_info < (3, 7):
        print("This application requires Python 3.7 or higher")
        sys.exit(1)
    
    # 运行主函数
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)