# core/response_handler.py
import asyncio
import random
from typing import Dict, Optional, Tuple
from utils.logger import get_logger

logger = get_logger()

class ResponseHandler:
    def __init__(self, config):
        self.config = config
        self.response_mode = config.responses.mode
        self.banners = config.responses.banners
        self.delay_before_close = config.responses.delay_before_close
        self.max_interactions = config.responses.max_interactions
        
        # 预定义的响应模板
        self.response_templates = self._init_response_templates()
    
    def _init_response_templates(self) -> Dict[int, Dict]:
        """初始化响应模板"""
        templates = {}
        
        # SSH响应模板
        ssh_banners = [
            "SSH-2.0-OpenSSH_7.4",
            "SSH-2.0-OpenSSH_8.0", 
            "SSH-2.0-OpenSSH_8.9p1 Ubuntu-3ubuntu0.1",
            "SSH-2.0-OpenSSH_8.2p1 Ubuntu-4ubuntu0.5"
        ]
        
        # FTP响应模板
        ftp_banners = [
            "220 Welcome to FTP server",
            "220 FTP server ready",
            "220 (vsFTPd 3.0.3)",
            "220 Microsoft FTP Service"
        ]
        
        # HTTP响应模板
        http_responses = [
            "HTTP/1.1 404 Not Found\r\nServer: nginx/1.18.0\r\nContent-Length: 0\r\n\r\n",
            "HTTP/1.1 404 Not Found\r\nServer: Apache/2.4.41\r\nContent-Length: 0\r\n\r\n",
            "HTTP/1.1 403 Forbidden\r\nServer: nginx\r\nContent-Length: 0\r\n\r\n",
            "HTTP/1.1 200 OK\r\nServer: nginx\r\nContent-Length: 0\r\n\r\n"
        ]
        
        # Telnet响应
        telnet_responses = [
            "login: ",
            "Username: ",
            "Welcome to Telnet Server\r\nlogin: "
        ]
        
        templates[22] = {
            'type': 'ssh',
            'banners': ssh_banners,
            'default': self.banners.get(22, "SSH-2.0-OpenSSH_8.0")
        }
        
        templates[21] = {
            'type': 'ftp',
            'banners': ftp_banners,
            'default': self.banners.get(21, "220 FTP server ready")
        }
        
        templates[80] = {
            'type': 'http',
            'banners': http_responses,
            'default': self.banners.get(80, "HTTP/1.1 404 Not Found\r\nServer: nginx\r\n\r\n")
        }
        
        templates[443] = {
            'type': 'https',
            'banners': [],
            'default': self.banners.get(443, "")  # HTTPS需要SSL握手，暂不响应
        }
        
        templates[23] = {
            'type': 'telnet',
            'banners': telnet_responses,
            'default': self.banners.get(23, "login: ")
        }
        
        templates[3389] = {
            'type': 'rdp',
            'banners': [],
            'default': ""  # RDP协议复杂，暂不响应
        }
        
        templates[1433] = {
            'type': 'mssql',
            'banners': [],
            'default': ""  # SQL Server协议复杂，暂不响应
        }
        
        templates[3306] = {
            'type': 'mysql',
            'banners': [],
            'default': ""  # MySQL协议复杂，暂不响应
        }
        
        return templates
    
    def get_banner(self, port: int, randomize: bool = True) -> Optional[str]:
        """获取指定端口的banner"""
        if self.response_mode == "none":
            return None
        
        template = self.response_templates.get(port)
        if not template:
            return None
        
        # 如果启用随机化且有多个banner选项
        if randomize and template['banners']:
            return random.choice(template['banners'])
        
        return template['default']
    
    async def handle_connection(self, reader: asyncio.StreamReader, 
                              writer: asyncio.StreamWriter, 
                              port: int, client_ip: str) -> Dict:
        """处理连接并返回连接信息"""
        connection_info = {
            'client_ip': client_ip,
            'port': port,
            'protocol': self._detect_protocol(port),
            'banner_sent': False,
            'data_received': b'',
            'response_sent': b'',
            'connection_duration': 0
        }
        
        start_time = asyncio.get_event_loop().time()
        
        try:
            # 根据模式处理响应
            if self.response_mode == "banner":
                await self._handle_banner_response(reader, writer, port, connection_info)
            elif self.response_mode == "limited_interactive":
                await self._handle_limited_interactive(reader, writer, port, connection_info)
            # mode == "none" 时直接关闭连接
            
        except Exception as e:
            logger.debug(f"Connection handling error for {client_ip}:{port} - {e}")
        finally:
            connection_info['connection_duration'] = asyncio.get_event_loop().time() - start_time
            await self._close_connection(writer)
        
        return connection_info
    
    async def _handle_banner_response(self, reader: asyncio.StreamReader,
                                    writer: asyncio.StreamWriter,
                                    port: int, connection_info: Dict):
        """处理banner响应模式"""
        banner = self.get_banner(port)
        
        if banner:
            try:
                # 发送banner
                writer.write(banner.encode() + b'\r\n')
                await writer.drain()
                
                connection_info['banner_sent'] = True
                connection_info['response_sent'] = banner.encode()
                
                logger.debug(f"Sent banner to {connection_info['client_ip']}:{port}: {banner[:50]}...")
                
                # 等待一小段时间模拟真实服务
                await asyncio.sleep(self.delay_before_close)
                
                # 尝试读取客户端发送的数据（如果有）
                try:
                    data = await asyncio.wait_for(reader.read(1024), timeout=1.0)
                    connection_info['data_received'] = data
                    logger.debug(f"Received data from {connection_info['client_ip']}:{port}: {len(data)} bytes")
                except asyncio.TimeoutError:
                    pass  # 客户端没有发送数据，正常情况
                
            except Exception as e:
                logger.debug(f"Error sending banner: {e}")
    
    async def _handle_limited_interactive(self, reader: asyncio.StreamReader,
                                        writer: asyncio.StreamWriter,
                                        port: int, connection_info: Dict):
        """处理有限交互模式"""
        banner = self.get_banner(port)
        interactions = 0
        
        # 发送初始banner
        if banner:
            try:
                writer.write(banner.encode() + b'\r\n')
                await writer.drain()
                connection_info['banner_sent'] = True
                connection_info['response_sent'] = banner.encode()
            except Exception as e:
                logger.debug(f"Error sending initial banner: {e}")
                return
        
        # 允许有限的交互
        while interactions < self.max_interactions:
            try:
                # 等待客户端数据
                data = await asyncio.wait_for(reader.read(1024), timeout=5.0)
                if not data:
                    break
                
                connection_info['data_received'] += data
                interactions += 1
                
                # 根据端口和接收到的数据生成响应
                response = self._generate_interactive_response(port, data, interactions)
                if response:
                    writer.write(response.encode() + b'\r\n')
                    await writer.drain()
                    connection_info['response_sent'] += response.encode()
                
                # 短暂延迟模拟真实服务
                await asyncio.sleep(0.1)
                
            except asyncio.TimeoutError:
                break  # 客户端没有继续发送数据
            except Exception as e:
                logger.debug(f"Error in limited interactive mode: {e}")
                break
    
    def _generate_interactive_response(self, port: int, data: bytes, interaction_count: int) -> Optional[str]:
        """生成交互式响应"""
        data_str = data.decode('utf-8', errors='ignore').strip()
        
        if port == 22:  # SSH
            if interaction_count == 1:
                return "Protocol mismatch."
        elif port == 21:  # FTP
            if 'USER' in data_str.upper():
                return "331 Password required"
            elif 'PASS' in data_str.upper():
                return "530 Login incorrect"
        elif port == 23:  # Telnet
            if interaction_count == 1:
                return "Password: "
            elif interaction_count == 2:
                return "Login incorrect"
        elif port == 80:  # HTTP
            if data_str.startswith('GET') or data_str.startswith('POST'):
                return "HTTP/1.1 404 Not Found\r\nContent-Length: 0\r\n"
        
        return None
    
    def _detect_protocol(self, port: int) -> str:
        """根据端口检测协议类型"""
        protocol_map = {
            22: 'SSH',
            21: 'FTP',
            23: 'Telnet',
            80: 'HTTP',
            443: 'HTTPS',
            3389: 'RDP',
            1433: 'MSSQL',
            3306: 'MySQL',
            5432: 'PostgreSQL',
            6379: 'Redis',
            27017: 'MongoDB'
        }
        return protocol_map.get(port, 'Unknown')
    
    async def _close_connection(self, writer: asyncio.StreamWriter):
        """安全关闭连接"""
        try:
            if not writer.is_closing():
                writer.close()
                await writer.wait_closed()
        except Exception as e:
            logger.debug(f"Error closing connection: {e}")
    
    def get_response_stats(self) -> Dict:
        """获取响应统计信息"""
        return {
            'response_mode': self.response_mode,
            'supported_ports': list(self.response_templates.keys()),
            'banner_count': len([t for t in self.response_templates.values() if t['banners']]),
            'max_interactions': self.max_interactions
        }