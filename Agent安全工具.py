# utils/security.py
import ipaddress
import socket
import time
from typing import List, Dict, Optional
from collections import defaultdict, deque
from utils.logger import get_logger

logger = get_logger()

class IPFilter:
    def __init__(self, whitelist_ips: List[str], ignore_internal: bool = True):
        self.whitelist_networks = []
        self.ignore_internal = ignore_internal
        self.last_alert_time: Dict[str, float] = defaultdict(float)
        
        # 解析白名单IP
        for ip_str in whitelist_ips:
            try:
                if '/' in ip_str:
                    # CIDR格式
                    self.whitelist_networks.append(ipaddress.ip_network(ip_str, strict=False))
                else:
                    # 单个IP
                    self.whitelist_networks.append(ipaddress.ip_network(f"{ip_str}/32", strict=False))
            except ValueError as e:
                logger.warning(f"Invalid IP in whitelist: {ip_str}, error: {e}")
    
    def is_whitelisted(self, ip: str) -> bool:
        """检查IP是否在白名单中"""
        try:
            ip_obj = ipaddress.ip_address(ip)
            
            # 检查是否在白名单网络中
            for network in self.whitelist_networks:
                if ip_obj in network:
                    return True
            
            # 检查是否忽略内网IP
            if self.ignore_internal and ip_obj.is_private:
                return True
            
            return False
            
        except ValueError:
            logger.warning(f"Invalid IP address: {ip}")
            return False
    
    def should_alert(self, ip: str, min_interval: int = 60) -> bool:
        """检查是否应该发送告警（基于时间间隔）"""
        if self.is_whitelisted(ip):
            return False
        
        current_time = time.time()
        last_time = self.last_alert_time.get(ip, 0)
        
        if current_time - last_time >= min_interval:
            self.last_alert_time[ip] = current_time
            return True
        
        return False

class ConnectionTracker:
    def __init__(self, max_history: int = 1000):
        self.connections: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_history))
        self.max_history = max_history
    
    def add_connection(self, ip: str, port: int, timestamp: float = None):
        """记录连接"""
        if timestamp is None:
            timestamp = time.time()
        
        self.connections[ip].append({
            'port': port,
            'timestamp': timestamp
        })
    
    def get_connection_count(self, ip: str, time_window: int = 300) -> int:
        """获取指定时间窗口内的连接数"""
        if ip not in self.connections:
            return 0
        
        current_time = time.time()
        count = 0
        
        for conn in self.connections[ip]:
            if current_time - conn['timestamp'] <= time_window:
                count += 1
        
        return count
    
    def get_port_scan_count(self, ip: str, time_window: int = 60) -> int:
        """获取端口扫描数量（不同端口数）"""
        if ip not in self.connections:
            return 0
        
        current_time = time.time()
        ports = set()
        
        for conn in self.connections[ip]:
            if current_time - conn['timestamp'] <= time_window:
                ports.add(conn['port'])
        
        return len(ports)
    
    def is_port_scanning(self, ip: str, threshold: int = 5, time_window: int = 60) -> bool:
        """判断是否为端口扫描行为"""
        return self.get_port_scan_count(ip, time_window) >= threshold
    
    def is_frequent_connection(self, ip: str, threshold: int = 10, time_window: int = 300) -> bool:
        """判断是否为频繁连接"""
        return self.get_connection_count(ip, time_window) >= threshold
    
    def cleanup_old_records(self, max_age: int = 3600):
        """清理过期记录"""
        current_time = time.time()
        
        for ip in list(self.connections.keys()):
            # 过滤掉过期的连接记录
            valid_connections = deque()
            for conn in self.connections[ip]:
                if current_time - conn['timestamp'] <= max_age:
                    valid_connections.append(conn)
            
            if valid_connections:
                self.connections[ip] = valid_connections
            else:
                del self.connections[ip]

class SecurityManager:
    def __init__(self, config):
        self.config = config
        self.ip_filter = IPFilter(
            config.filtering.whitelist_ips,
            config.filtering.ignore_internal
        )
        self.connection_tracker = ConnectionTracker()
        
        # 定期清理任务
        self._last_cleanup = time.time()
        self._cleanup_interval = 3600  # 1小时清理一次
    
    def is_allowed_connection(self, ip: str, port: int) -> bool:
        """检查连接是否被允许"""
        # 检查IP白名单
        if self.ip_filter.is_whitelisted(ip):
            return False  # 白名单IP不记录
        
        # 记录连接
        self.connection_tracker.add_connection(ip, port)
        
        # 检查是否需要告警
        return self.ip_filter.should_alert(ip, self.config.filtering.min_alert_interval)
    
    def analyze_behavior(self, ip: str) -> Dict[str, any]:
        """分析攻击行为"""
        behavior = {
            'ip': ip,
            'is_port_scanning': self.connection_tracker.is_port_scanning(ip),
            'is_frequent_connection': self.connection_tracker.is_frequent_connection(ip),
            'connection_count_5min': self.connection_tracker.get_connection_count(ip, 300),
            'port_scan_count_1min': self.connection_tracker.get_port_scan_count(ip, 60),
            'alert_level': 'INFO'
        }
        
        # 确定告警级别
        if behavior['is_port_scanning'] and behavior['port_scan_count_1min'] >= 10:
            behavior['alert_level'] = 'CRITICAL'
        elif behavior['is_port_scanning'] or behavior['is_frequent_connection']:
            behavior['alert_level'] = 'WARNING'
        
        return behavior
    
    def get_geo_info(self, ip: str) -> Optional[Dict[str, str]]:
        """获取IP地理位置信息（可选功能）"""
        # 这里可以集成GeoIP库或调用外部API
        # 暂时返回空，避免依赖外部服务
        try:
            # 简单的IP归属地判断
            ip_obj = ipaddress.ip_address(ip)
            if ip_obj.is_private:
                return {'country': 'Private', 'city': 'Internal'}
            elif ip_obj.is_loopback:
                return {'country': 'Local', 'city': 'Localhost'}
            else:
                return {'country': 'Unknown', 'city': 'Unknown'}
        except:
            return None
    
    def periodic_cleanup(self):
        """定期清理任务"""
        current_time = time.time()
        if current_time - self._last_cleanup >= self._cleanup_interval:
            self.connection_tracker.cleanup_old_records()
            self._last_cleanup = current_time
            logger.debug("Performed periodic cleanup of connection records")