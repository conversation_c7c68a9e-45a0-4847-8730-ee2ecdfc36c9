# docker-compose.yml
version: '3.8'

services:
  honeypot-agent:
    build: .
    container_name: honeypot-agent
    restart: unless-stopped
    
    # 网络模式 - 使用host网络以便监听多个端口
    network_mode: host
    
    # 环境变量
    environment:
      - PYTHONUNBUFFERED=1
      - TZ=Asia/Shanghai
    
    # 挂载配置和数据目录
    volumes:
      - ./config:/app/config:ro
      - ./logs:/app/logs
      - ./data:/app/data
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    
    # 健康检查
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8080/health', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

# 可选：如果不使用host网络，可以使用以下配置
# networks:
#   honeypot-net:
#     driver: bridge
#     ipam:
#       config:
#         - subnet: **********/16