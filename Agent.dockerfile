# Dockerfile
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    libc6-dev \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p logs data config

# 设置权限
RUN chmod +x main.py

# 创建非root用户
RUN useradd -m -u 1000 honeypot && \
    chown -R honeypot:honeypot /app

USER honeypot

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD python -c "import socket; s=socket.socket(); s.settimeout(1); s.connect(('localhost', 22)); s.close()" || exit 1

# 暴露端口（示例，实际端口由配置决定）
EXPOSE 22 21 23 80 443 3389 1433 3306

# 启动命令
CMD ["python", "main.py", "-c", "config/agent.yaml"]