# utils/logger.py
import logging
import logging.handlers
import os
from pathlib import Path
from typing import Optional

class AgentLogger:
    def __init__(self, 
                 name: str = "honeypot-agent",
                 log_level: str = "INFO",
                 log_dir: str = "logs",
                 max_log_size: str = "100MB",
                 log_rotation: int = 7):
        
        self.name = name
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 解析日志大小
        self.max_bytes = self._parse_size(max_log_size)
        self.backup_count = log_rotation
        
        # 创建logger
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # 避免重复添加handler
        if not self.logger.handlers:
            self._setup_handlers()
    
    def _parse_size(self, size_str: str) -> int:
        """解析大小字符串，如 '100MB' -> 字节数"""
        size_str = size_str.upper()
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    def _setup_handlers(self):
        """设置日志处理器"""
        # 文件处理器 - 轮转日志
        file_handler = logging.handlers.RotatingFileHandler(
            filename=self.log_dir / f"{self.name}.log",
            maxBytes=self.max_bytes,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        
        # 设置格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 添加处理器
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def get_logger(self) -> logging.Logger:
        """获取logger实例"""
        return self.logger
    
    def info(self, msg: str, *args, **kwargs):
        """记录INFO级别日志"""
        self.logger.info(msg, *args, **kwargs)
    
    def warning(self, msg: str, *args, **kwargs):
        """记录WARNING级别日志"""
        self.logger.warning(msg, *args, **kwargs)
    
    def error(self, msg: str, *args, **kwargs):
        """记录ERROR级别日志"""
        self.logger.error(msg, *args, **kwargs)
    
    def debug(self, msg: str, *args, **kwargs):
        """记录DEBUG级别日志"""
        self.logger.debug(msg, *args, **kwargs)
    
    def critical(self, msg: str, *args, **kwargs):
        """记录CRITICAL级别日志"""
        self.logger.critical(msg, *args, **kwargs)

# 全局logger实例
_logger_instance: Optional[AgentLogger] = None

def get_logger(name: str = "honeypot-agent", **kwargs) -> AgentLogger:
    """获取全局logger实例"""
    global _logger_instance
    if _logger_instance is None:
        _logger_instance = AgentLogger(name, **kwargs)
    return _logger_instance

def init_logger(config):
    """初始化logger"""
    global _logger_instance
    _logger_instance = AgentLogger(
        name="honeypot-agent",
        log_level=config.security.log_level,
        max_log_size=config.security.max_log_size,
        log_rotation=config.security.log_rotation
    )
    return _logger_instance